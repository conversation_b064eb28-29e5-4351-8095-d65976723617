export interface InvoiceInfoItem {
  id?: number;
  customer_invoice_name: string;
  customer_deposit_bank: string;
  customer_deposit_bank_sub: string;
  customer_bank_account_name: string;
  customer_bank_account: string;
  customer_tax_number: string;
  customer_invoice_type: InvoiceType;
  customer_receive_explain?: string;
  customer_verify_type: CustomerVerifyType;
  customer_fare_order: CustomerFareOrder;
  is_unusual_need: IsUnusualNeed;
  unusual_need_explain?: string;
  is_open_charge: IsOpenCharge;
  postal_type: PostalType;
  postal_address: string;
  account_seq: string;
  charge_start_day: string;
  charge_end_day: string;
  status?: string;
  create_user?: string;
  created_at?: string;
}

export interface InvoiceFormData
  extends Omit<
    InvoiceInfoItem,
    | "created_at"
    | "create_user"
    | "status"
    | "charge_start_day"
    | "charge_end_day"
  > {
  charge_start_day: Date;
  charge_end_day: Date;
}

export enum InvoiceType {
  VAT_SPECIAL_TICKET = "增值税专票",
  VAT_GENERAL_TICKET = "增值税普票",
  INVOICE = "invoice",
}

export enum CustomerVerifyType {
  NO_VERIFY = "不对账",
  VERIFY_NO_CONFIRM = "对账无需确认",
  VERIFY_EXPIRED_NO_CONFIRM = "对账超期无需确认",
  VERIFY_NEED_CONFIRM = "对账要确认",
}

export enum CustomerFareOrder {
  TICKET_FIRST = "先票后款",
  PAYMENT_FIRST = "先款后票",
}

export enum IsUnusualNeed {
  YES = "是",
  NO = "否",
}

export enum IsOpenCharge {
  SUSPENDED = "暂停",
  ACTIVE = "开账",
  TERMINATED = "终止",
}

export enum PostalType {
  EMAIL = "邮件",
  EXPRESS = "快递",
  EMAIL_AND_EXPRESS = "邮件且快递",
  NONE = "无需",
}

export enum InvoiceStatus {
  DRAFT = "暂存",
  PENDING_REVIEW = "待账务审核",
  APPROVED = "审核生效",
  CANCELED = "注销",
}

export interface InvoiceSearchParams {
  account_seq?: string;
  create_user?: string;
  customer_invoice_name?: string;
  page?: number;
  pageSize?: number;
}

// 分账序号简单信息（用于下拉选择）
export interface AccountSeqSimpleInfo {
  id: number;
  account_seq: string;
  seq_name: string;
}

// 后付费发票数据结构
export interface PostPaidInvoiceItem {
  id: number;
  sub_order_no: string;
  account_seq: string;
  charge_month: number;
  fee_amount: string;
  tax: number;
  tax_type: string;
  income_type: string;
  pay_type: string;
  customer_name: string;
  adjust_month: number | null;
  invoice_amount: string;
}

// 后付费发票搜索参数
export interface PostPaidInvoiceSearchParams {
  account_seq?: string;
  charge_month?: number;
  customer_name?: string;
  page?: number;
  pageSize?: number;
  sub_order_no?: string;
}

// 预付费开票相关类型
export interface PrePaidInvoiceItem {
  id: number;
  order_num: string;
  total_num: string;
  income_type: string;
  account_seq: string;
  bill_status: string;
  customer_name: string;
  pay_type: string;
  currency_type: string;
}

export interface PrePaidInvoiceSearchParams {
  account_seq?: string;
  customer_num?: string;
  page?: number;
  pageSize?: number;
  total_num?: string;
}

// 预付费开票订单信息项 (用于表单)
export interface PrePaidInvoiceOrderInfo {
  amount: string;
  invoice_month: Date;
}

// 预付费开票订单信息项（用于API请求）
export interface PrePaidInvoiceOrderInfoRequest {
  amount: string;
  invoice_month: number;
}

// 预付费开票订单项（用于表单）
export interface PrePaidInvoiceOrderItem {
  order_id: number;
  order_num?: string; // 订单编号，用于显示
  order_info: PrePaidInvoiceOrderInfo[];
}

// 预付费开票订单项（用于API请求）
export interface PrePaidInvoiceOrderItemRequest {
  order_id: number;
  order_num?: string; // 订单编号，用于显示
  order_info: PrePaidInvoiceOrderInfoRequest[];
}

// 预付费开票请求（用于表单）
export interface PrePaidInvoiceRequest {
  orders: PrePaidInvoiceOrderItem[];
  amount: string;
  account_seq: string;
  invoice_info_id: number;
  invoice_type: string;
  tax_rate: number;
  tax_amount: string;
  currency_type: string;
  invoice_currency_type: string;
  exchange_rate: string;
  signing_entity: string;
  customer_num: string;
  remark: string;
}

// 预付费开票请求（用于API）
export interface PrePaidInvoiceApiRequest {
  orders: PrePaidInvoiceOrderItemRequest[];
  amount: string;
  account_seq: string;
  invoice_info_id: number;
  invoice_type: string;
  tax_rate: number;
  tax_amount: string;
  currency_type: string;
  invoice_currency_type: string;
  exchange_rate: string;
  signing_entity: string;
  customer_num: string;
  remark: string;
}

// 分账序号详细信息
export interface AccountSeqDetailInfo {
  id: number;
  customer_num: string;
  customer_name: string;
  account_seq: string;
  seq_name: string;
  tax: number;
  created_at: string;
}
